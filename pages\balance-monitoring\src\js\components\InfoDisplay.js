/**
 * 信息展示组件
 */
import { buildRealtimeDataUrl } from "../config/ChartConfig.js";

class InfoDisplay {
  constructor(containerId, data = null) {
    this.container = document.getElementById(containerId);
    this.data = data;
    this.apiUrl = buildRealtimeDataUrl(["REAL_TIME_ACE", "CURRENT_COST"]);
    this.init();
  }

  init() {
    if (!this.container) {
      console.error("容器元素不存在");
      return;
    }

    // 如果没有传入数据，则从接口获取
    if (!this.data) {
      this.fetchData();
    } else {
      this.render();
    }
  }

  // 从接口获取数据
  async fetchData() {
    try {
      const response = await fetch(this.apiUrl);
      const result = await response.json();

      if (result.code === "0000" && result.data && Array.isArray(result.data)) {
        // 转换接口数据为组件格式
        this.data = this.transformApiData(result.data);
        this.render();
      } else {
        console.error("接口返回错误:", result);
        // this.showError("数据获取失败");
      }
    } catch (error) {
      console.error("接口调用失败:", error);
      // this.showError("网络请求失败");
    }
  }

  // 转换接口数据为组件格式
  transformApiData(apiData) {
    // 根据API文档，返回数据按参数顺序排列
    // 第一个参数是实时ACE (130010:320000000000010034)
    // 第二个参数是当班费用 (130037:320000000000010010)
    return {
      realTimeACE: this.formatNumber(apiData[0]),
      currentCost: this.formatNumber(apiData[1]),
    };
  }

  // 格式化数字为1位小数
  formatNumber(value) {
    if (value === null || value === undefined || value === "") {
      return "N/A";
    }

    const num = parseFloat(value);
    if (isNaN(num)) {
      return value; // 如果不是数字，返回原值
    }

    return num.toFixed(1);
  }

  // 显示错误信息
  showError(message) {
    const costValueElement = this.container.querySelector(
      ".info-box:nth-child(1) .info-box-value"
    );
    const aceValueElement = this.container.querySelector(
      ".info-box:nth-child(2) .info-box-value"
    );

    if (costValueElement && aceValueElement) {
      costValueElement.textContent = message;
      aceValueElement.textContent = message;
    }
  }

  render() {
    if (!this.data) {
      this.showError("数据格式错误");
      return;
    }

    // 获取当班费用和实时ACE的容器
    const costValueElement = this.container.querySelector(
      ".info-box:nth-child(1) .info-box-value"
    );
    const aceValueElement = this.container.querySelector(
      ".info-box:nth-child(2) .info-box-value"
    );

    if (costValueElement && aceValueElement) {
      costValueElement.textContent = this.data.currentCost;
      aceValueElement.textContent = this.data.realTimeACE;
    } else {
      console.error("信息显示元素不存在");
    }
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  // 刷新数据（重新从接口获取）
  refreshData() {
    this.fetchData();
  }
}

export default InfoDisplay;
