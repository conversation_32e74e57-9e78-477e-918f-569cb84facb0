import { defineConfig } from "vite";
import { resolve } from "path";

// 多页面配置
const pages = {
  main: {
    entry: resolve(__dirname, "pages/balance-monitoring/index.html"),
    name: "balance-monitoring",
    title: "全网平衡监视系统",
  },
};

// 构建多页面入口配置
function buildMultiPageInput() {
  const input = {};
  Object.keys(pages).forEach((key) => {
    const page = pages[key];
    input[key] = page.entry;
  });
  return input;
}

export default defineConfig({
  // 开发服务器配置
  server: {
    port: 3000,
    open: "/pages.html", // 默认打开页面导航，可以选择访问 balance-monitoring.html
    // 代理配置
    proxy: {
      "/dwyztApp/dwyzt": {
        target: "http://*************:8087/mock/189/dwyzt",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dwyztApp\/dwyzt/, ""),
      },
    },
  },
  // 构建配置
  build: {
    outDir: "dist",
    assetsDir: "assets",
    sourcemap: false,
    minify: "terser",
    // 分包策略
    rollupOptions: {
      input: buildMultiPageInput(),
      output: {
        // JS文件分离配置
        chunkFileNames: (chunkInfo) => {
          // 第三方库放在vendor目录
          if (chunkInfo.name === "vendor") {
            return "js/vendor/[name].[hash].js";
          }
          // 其他chunk放在chunks目录
          return "js/chunks/[name].[hash].js";
        },
        // 入口文件配置
        entryFileNames: "js/[name].[hash].js",
        // 静态资源文件配置
        assetFileNames: (assetInfo) => {
          const fileName = assetInfo.names?.[0] || "unknown";
          const info = fileName.split(".");
          const ext = info[info.length - 1];

          // 图片资源
          if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(fileName)) {
            return "assets/images/[name].[hash].[ext]";
          }
          // 字体资源
          if (/\.(woff2?|eot|ttf|otf)$/i.test(fileName)) {
            return "assets/fonts/[name].[hash].[ext]";
          }
          // CSS文件
          if (ext === "css") {
            return "assets/css/[name].[hash].[ext]";
          }
          // 其他资源
          return "assets/[name].[hash].[ext]";
        },
      },
    },
    // 压缩配置
    terserOptions: {
      compress: {
        drop_console: true, // 生产环境移除console
        drop_debugger: true, // 生产环境移除debugger
      },
    },
    // 分包大小警告配置
    chunkSizeWarningLimit: 1000,
  },

  // 基础路径配置
  base: "./",

  // 静态资源处理
  assetsInclude: ["**/*.webp"],
});
