import { defineConfig } from "vite";
import { resolve } from "path";

// 多页面配置
const pages = {
  main: {
    entry: resolve(__dirname, "pages/balance-monitoring/index.html"),
    name: "balance-monitoring",
    title: "全网平衡监视系统",
  },
};

// 构建多页面入口配置
function buildMultiPageInput() {
  const input = {};
  Object.keys(pages).forEach((key) => {
    const page = pages[key];
    input[key] = page.entry;
  });
  return input;
}

export default defineConfig({
  // 开发服务器配置
  server: {
    port: 3000,
    open: "/pages.html", // 默认打开页面导航，可以选择访问 balance-monitoring.html
    // 代理配置
    proxy: {
      "/dwyztApp/dwyzt": {
        target: "http://*************:8087/mock/189/dwyzt",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dwyztApp\/dwyzt/, ""),
      },
    },
  },
  // 构建配置
  build: {
    outDir: "dist",
    assetsDir: "assets",
    sourcemap: false,
    minify: "terser",
    // 分包策略
    rollupOptions: {
      input: buildMultiPageInput(),
      output: {
        // JS文件分离配置 - 按页面分组
        chunkFileNames: (chunkInfo) => {
          // 第三方库放在对应页面的vendor目录
          if (chunkInfo.name === "vendor") {
            return "pages/balance-monitoring/js/vendor/[name].[hash].js";
          }
          // 其他chunk放在对应页面的chunks目录
          return "pages/balance-monitoring/js/chunks/[name].[hash].js";
        },
        // 入口文件配置 - 按页面分组
        entryFileNames: (chunkInfo) => {
          // 根据入口文件名确定页面
          if (chunkInfo.name === "main") {
            return "pages/balance-monitoring/js/[name].[hash].js";
          }
          return "pages/balance-monitoring/js/[name].[hash].js";
        },
        // 静态资源文件配置 - 按页面分组
        assetFileNames: (assetInfo) => {
          const fileName = assetInfo.names?.[0] || "unknown";
          const info = fileName.split(".");
          const ext = info[info.length - 1];

          // 图片资源
          if (/\.(png|jpe?g|gif|svg|webp|ico)$/i.test(fileName)) {
            return "pages/balance-monitoring/assets/images/[name].[hash].[ext]";
          }
          // 字体资源
          if (/\.(woff2?|eot|ttf|otf)$/i.test(fileName)) {
            return "pages/balance-monitoring/assets/fonts/[name].[hash].[ext]";
          }
          // CSS文件
          if (ext === "css") {
            return "pages/balance-monitoring/css/[name].[hash].[ext]";
          }
          // 其他资源
          return "pages/balance-monitoring/assets/[name].[hash].[ext]";
        },
        // 手动分包配置 - 启用代码切分
        manualChunks: (id) => {
          // 第三方库分包
          if (id.includes("node_modules")) {
            // 大型库单独分包
            if (id.includes("echarts")) {
              return "echarts";
            }
            if (id.includes("dayjs")) {
              return "dayjs";
            }
            if (id.includes("axios")) {
              return "axios";
            }
            // 其他第三方库
            return "vendor";
          }
          // 业务代码分包
          if (id.includes("src/js/")) {
            // 根据文件路径进行分包
            if (id.includes("components/")) {
              return "components";
            }
            if (id.includes("utils/")) {
              return "utils";
            }
            if (id.includes("api/")) {
              return "api";
            }
            if (id.includes("charts/")) {
              return "charts";
            }
          }
        },
      },
    },
    // 压缩配置
    terserOptions: {
      compress: {
        drop_console: true, // 生产环境移除console
        drop_debugger: true, // 生产环境移除debugger
      },
    },
    // 分包大小警告配置
    chunkSizeWarningLimit: 1000,
  },

  // 基础路径配置
  base: "./",

  // 静态资源处理
  assetsInclude: ["**/*.webp"],
});
